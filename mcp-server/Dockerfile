# Dockerfile for standalone MCP PostgreSQL server
FROM node:18-alpine

# Install the MCP PostgreSQL server globally
RUN npm install -g @modelcontextprotocol/server-postgres

# Create a simple startup script
COPY start-mcp-server.sh /usr/local/bin/start-mcp-server.sh
RUN chmod +x /usr/local/bin/start-mcp-server.sh

# Expose port for HTTP transport (if needed in future)
EXPOSE 3001

# Start the MCP server
CMD ["/usr/local/bin/start-mcp-server.sh"]
