version: '3.8'

services:
  # NestJS Application
  nestjs-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY:-lm-studio}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-http://127.0.0.1:1234/v1}
      - OPENAI_MODEL=${OPENAI_MODEL:-devstral-small-2505-mlx}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0.1}
      - DATABASE_URL=${DATABASE_URL:-postgresql://postgres:<EMAIL>:5433/sw_dev}
      - DATABASE_HOST=${DATABASE_HOST:-itrdev.lan}
      - DATABASE_PORT=${DATABASE_PORT:-5433}
      - DATABASE_USERNAME=${DATABASE_USERNAME:-postgres}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD:-postgres}
      - DATABASE_NAME=${DATABASE_NAME:-sw_dev}
      - MCP_ENABLED=true
      - MCP_POSTGRES_URL=http://mcp-postgres:3001
      - CONTEXT_INCLUDE_STATIC_DESCRIPTION=${CONTEXT_INCLUDE_STATIC_DESCRIPTION:-true}
      - CONTEXT_DESCRIPTION_FILE=${CONTEXT_DESCRIPTION_FILE:-docs/database-description.md}
    depends_on:
      - mcp-postgres
    networks:
      - app-network
    volumes:
      - ./docs:/app/docs:ro

  # PostgreSQL MCP Server
  mcp-postgres:
    build: ./mcp-server
    environment:
      - DATABASE_URL=${DATABASE_URL:-postgresql://postgres:<EMAIL>:5433/sw_dev}
    networks:
      - app-network
    # MCP server is only accessible within the docker network
    # No external ports exposed for security

networks:
  app-network:
    driver: bridge
