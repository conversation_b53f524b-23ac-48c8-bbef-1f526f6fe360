import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class ContextService {
  private readonly logger = new Logger(ContextService.name);
  private databaseDescription: string | null = null;

  constructor(private configService: ConfigService) {
    this.loadDatabaseDescription();
    this.logger.log('Context Service initialized successfully');
  }

  getSystemPromptWithContext(): string {
    this.logger.log('Generating system prompt with database context');

    const contextConfig = this.configService.get('context');

    let systemPrompt = `You are a helpful database assistant. Follow these steps precisely:

1. Read the user's request carefully.
2. To fetch data, call **exactly one** query tool:
   • **Only** \`SELECT …\` statements.
   • **Never** \`INSERT\`, \`UPDATE\`, \`DELETE\`, \`CREATE\`, \`DROP\`, or \`ALTER\`.
   • Add a reasonable \`LIMIT\` (≤ 50) unless the user explicitly asks for all rows.
3. Wait for the tool result.
4. Compose a concise, helpful answer for the user:
   • Explain in plain language what you did.
   • Summarize or tabulate key rows; do not dump large raw JSON.
   • If no rows are returned, say so and suggest what data might be missing.

Always use the OpenAI **tool-calling interface** (no custom tags or wrappers).
If a question can be answered without the database, respond directly.

When citing table or column names, keep their exact PostgreSQL casing.

(You are connected to a production replica; treat the data with care.)`;

    if (contextConfig.includeStaticDescription) {
      systemPrompt += '\n\n' + this.getStaticDatabaseDescription();
    }

    return systemPrompt;
  }

  private loadDatabaseDescription(): void {
    try {
      const contextConfig = this.configService.get('context');
      const descriptionPath = contextConfig.descriptionFile || 'docs/database-description.md';

      // Resolve path relative to project root
      const fullPath = join(process.cwd(), descriptionPath);

      this.databaseDescription = readFileSync(fullPath, 'utf-8');
      this.logger.log(`Loaded database description from: ${descriptionPath}`);
    } catch (error) {
      this.logger.warn(`Failed to load database description: ${error.message}`);
      this.databaseDescription = this.getDefaultDescription();
    }
  }

  private getStaticDatabaseDescription(): string {
    if (this.databaseDescription) {
      return this.databaseDescription;
    }

    return this.getDefaultDescription();
  }

  private getDefaultDescription(): string {
    return `## DATABASE STRUCTURE OVERVIEW

This database contains various tables for data analysis. Use the available query tools to explore the structure and data.
Create a database description file at docs/database-description.md to provide specific schema information.`;
  }
}
