# Application
PORT=3000
NODE_ENV=development

# OpenAI/LM Studio Configuration
OPENAI_API_KEY=lm-studio
OPENAI_BASE_URL=http://127.0.0.1:1234/v1
OPENAI_MODEL=devstral-small-2505-mlx
OPENAI_TEMPERATURE=0.1

# PostgreSQL Database
DATABASE_URL=postgresql://postgres:<EMAIL>:5433/sw_dev
DATABASE_HOST=itrdev.lan
DATABASE_PORT=5433
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=sw_dev

# MCP Configuration
MCP_ENABLED=true
MCP_POSTGRES_URL=http://mcp-postgres:3001

# Context Configuration
CONTEXT_INCLUDE_STATIC_DESCRIPTION=true
CONTEXT_DESCRIPTION_FILE=docs/database-description.md
CONTEXT_SYSTEM_PROMPT_FILE=docs/system-prompt.txt
